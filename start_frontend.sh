#!/bin/bash

# ADC Data Validation UI - Frontend Startup Script
# This script starts the React frontend development server with environment configuration

set -e  # Exit on any error

echo "🚀 Starting ADC Data Validation UI Frontend..."

# Change to frontend directory
cd "$(dirname "$0")/frontend"

# Check if .env file exists
if [ ! -f .env ]; then
    echo "⚠️  .env file not found. Copying from .env.example..."
    cp .env.example .env
    echo "✅ Created .env file. Please review and update the configuration."
fi

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
else
    echo "📦 Dependencies already installed."
fi

# Load environment variables for display
if [ -f .env ]; then
    echo "📋 Environment configuration:"
    echo "   App Name: $(grep VITE_APP_NAME .env | cut -d '=' -f2)"
    echo "   API URL: $(grep VITE_API_BASE_URL .env | cut -d '=' -f2)"
    echo "   Debug Mode: $(grep VITE_DEBUG_MODE .env | cut -d '=' -f2)"
fi

echo "🌐 Starting Vite development server..."
echo "   Host: $(grep VITE_DEV_SERVER_HOST .env | cut -d '=' -f2)"
echo "   Port: $(grep VITE_DEV_SERVER_PORT .env | cut -d '=' -f2)"
echo "   Note: Server will be accessible from external hosts for port forwarding"

# Start the development server
npm run dev
